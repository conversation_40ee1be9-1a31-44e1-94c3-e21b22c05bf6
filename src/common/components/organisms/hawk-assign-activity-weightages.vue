<script setup>
import { cloneDeep } from 'lodash-es';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';

const props = defineProps({
  showWeights: {
    type: Boolean,
    default: false,
  },
  tableData: {
    type: Array,
    required: true,
  },
  showSync: {
    type: <PERSON>olean,
    default: false,
  },
  syncHandler: {
    type: Function,
    default: () => {},
  },
  noteDescription: {
    type: String,
    required: true,
  },
});

const { $t } = useCommonImports();

const hot_columns = [
  {
    data: 'name',
    readOnly: true,
    header: $t('Activity'),
  },
  {
    data: 'weight',
    type: 'numeric',
    config: {
      required: true,
      field_type: 'numeric',
      min: 0,
      max: 100,
    },
    validator: 'default-validator',
    header: $t('Weight'),
  },
];

const state = reactive({
  form_data: {
    show_weights: cloneDeep(props.showWeights),
  },
  hot_instance: null,
  hot_table_data: [],
  is_syncing: false,
  reload_count: Number.MIN_SAFE_INTEGER,
});

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_table_data.length + 1) * 30 + 7;
  if (calculated_height > 500)
    calculated_height = 500;
  return `${calculated_height}px`;
});

const total_weights = computed(() => {
  return state.hot_table_data.reduce((sum, row) => {
    const w = Number(row.weight);
    return sum + (Number.isNaN(w) ? 0 : w);
  }, 0);
});

const weightage_info_message = computed(() => {
  if (total_weights.value === 100) {
    return $t('The sum of weights is 100%.');
  }
  else if (total_weights.value > 100) {
    return `${$t('The sum of weights exceed 100% by')} ${(Math.abs(total_weights.value) - 100).toFixed(2)}%`;
  }
  return `${$t('The sum of weights is less than 100% by')} ${(100 - total_weights.value).toFixed(2)}%`;
});

async function onSync() {
  state.is_syncing = true;
  await props.syncHandler();
  state.is_syncing = false;
}

function getTerraActivityWeights() {
  let is_data_invalid = false;
  state.hands_on_table_instance?.validateCells?.((is_valid) => {
    is_data_invalid = !is_valid;
  });
  is_data_invalid = is_data_invalid || total_weights.value !== 100;
  return {
    is_data_invalid,
    show_weights: state.form_data.show_weights,
    data: cloneDeep(state.hot_table_data),
  };
}
defineExpose({ getTerraActivityWeights });

watch(() => props.tableData, () => {
  state.hot_table_data = cloneDeep(props.tableData);
  state.reload_count++;
}, { immediate: true, deep: true });
</script>

<template>
  <Vueform
    v-model="state.form_data"
    size="sm"
    sync
    :display-errors="false"
  >
    <div class="col-span-12 flex flex-col gap-y-3">
      <div class="flex justify-between items-center">
        <CheckboxElement
          name="show_weights"
        >
          {{ $t('Assign weights to activities') }}
        </CheckboxElement>
        <div v-if="state.form_data.show_weights && props.showSync" class="flex justify-end">
          <HawkButton :loading="state.is_syncing" size="xs" type="text" class="w-fit" @click="onSync()">
            <IconHawkRefreshCcwFive class="w-4 h-4" />
            {{ $t('Sync') }}
          </HawkButton>
        </div>
      </div>
      <template v-if="state.form_data.show_weights">
        <HawkHandsontable
          :key="state.reload_count"
          :data="state.hot_table_data"
          :columns="hot_columns"
          :col-headers="hot_columns.map(column => column.header)"
          :hot-settings="{
            rowHeights: '30px',
            contextMenu: false,
            allowRemoveRow: false,
          }"
          :height="hot_table_height"
          :add-new-row-on-enter="false"
          @ready="state.hot_instance = $event"
        />
        <HawkAlertBanner
          :color="total_weights === 100 ? 'success' : 'error'"
          class="!p-2 text-xs font-medium"
        >
          <template #icon>
            <IconHawkCheckCircle v-if="total_weights === 100" />
            <IconHawkAlertTriangle v-else />
          </template>
          <template #content>
            {{ weightage_info_message }}
          </template>
        </HawkAlertBanner>
        <div>
          <span class="text-xs font-semibold text-gray-900">
            {{ $t('Note') }}:
          </span>
          <span class="text-xs font-normal text-gray-600">
            {{ props.noteDescription }}
          </span>
        </div>
      </template>
    </div>
  </Vueform>
</template>
